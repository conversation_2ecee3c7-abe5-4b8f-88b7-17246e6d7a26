import React, { useState, useEffect } from "react"
import TodoTaskList, { TodoTask, TaskStatus } from "./TodoTaskList"

/**
 * TodoTaskList 使用示例
 * 
 * 这个示例展示了如何在实际项目中集成和使用 TodoTaskList 组件
 */

// 模拟本地存储的键名
const STORAGE_KEY = 'cline_todo_tasks'

// 从本地存储加载任务
const loadTasksFromStorage = (): TodoTask[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      const tasks = JSON.parse(stored)
      // 转换日期字符串回Date对象
      return tasks.map((task: any) => ({
        ...task,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt)
      }))
    }
  } catch (error) {
    console.error('Failed to load tasks from storage:', error)
  }
  return []
}

// 保存任务到本地存储
const saveTasksToStorage = (tasks: TodoTask[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks))
  } catch (error) {
    console.error('Failed to save tasks to storage:', error)
  }
}

// 生成唯一ID
const generateId = (): string => {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const TodoTaskListExample: React.FC = () => {
  const [tasks, setTasks] = useState<TodoTask[]>([])

  // 组件挂载时加载任务
  useEffect(() => {
    const loadedTasks = loadTasksFromStorage()
    setTasks(loadedTasks)
  }, [])

  // 任务变化时保存到本地存储
  useEffect(() => {
    saveTasksToStorage(tasks)
  }, [tasks])

  // 添加新任务
  const handleTaskAdd = (newTask: Omit<TodoTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    const task: TodoTask = {
      ...newTask,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    setTasks(prevTasks => [...prevTasks, task])
    
    // 可选：添加成功提示
    console.log('Task added:', task.title)
  }

  // 更新任务
  const handleTaskUpdate = (taskId: string, updates: Partial<TodoTask>) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === taskId 
          ? { ...task, ...updates, updatedAt: new Date() }
          : task
      )
    )
    
    // 可选：更新成功提示
    console.log('Task updated:', taskId, updates)
  }

  // 删除任务
  const handleTaskDelete = (taskId: string) => {
    const taskToDelete = tasks.find(task => task.id === taskId)
    
    setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId))
    
    // 可选：删除成功提示
    console.log('Task deleted:', taskToDelete?.title)
  }

  // 批量操作示例
  const handleClearCompleted = () => {
    const completedCount = tasks.filter(task => task.status === TaskStatus.COMPLETED).length
    
    if (completedCount === 0) {
      alert('没有已完成的任务需要清理')
      return
    }
    
    if (confirm(`确定要删除 ${completedCount} 个已完成的任务吗？`)) {
      setTasks(prevTasks => 
        prevTasks.filter(task => task.status !== TaskStatus.COMPLETED)
      )
      console.log(`Cleared ${completedCount} completed tasks`)
    }
  }

  // 标记所有任务为完成
  const handleMarkAllCompleted = () => {
    const incompleteCount = tasks.filter(task => task.status !== TaskStatus.COMPLETED).length
    
    if (incompleteCount === 0) {
      alert('所有任务都已完成')
      return
    }
    
    if (confirm(`确定要将 ${incompleteCount} 个未完成任务标记为完成吗？`)) {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.status !== TaskStatus.COMPLETED
            ? { ...task, status: TaskStatus.COMPLETED, updatedAt: new Date() }
            : task
        )
      )
      console.log(`Marked ${incompleteCount} tasks as completed`)
    }
  }

  // 重置所有失败任务
  const handleResetFailedTasks = () => {
    const failedCount = tasks.filter(task => task.status === TaskStatus.FAILED).length
    
    if (failedCount === 0) {
      alert('没有失败的任务需要重置')
      return
    }
    
    if (confirm(`确定要重置 ${failedCount} 个失败任务为新建状态吗？`)) {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.status === TaskStatus.FAILED
            ? { ...task, status: TaskStatus.NEW, updatedAt: new Date() }
            : task
        )
      )
      console.log(`Reset ${failedCount} failed tasks`)
    }
  }

  // 统计信息
  const stats = {
    total: tasks.length,
    new: tasks.filter(task => task.status === TaskStatus.NEW).length,
    inProgress: tasks.filter(task => task.status === TaskStatus.IN_PROGRESS).length,
    completed: tasks.filter(task => task.status === TaskStatus.COMPLETED).length,
    failed: tasks.filter(task => task.status === TaskStatus.FAILED).length,
  }

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2 style={{ color: 'var(--vscode-foreground)', marginBottom: '20px' }}>
        TodoTaskList 集成示例
      </h2>
      
      {/* 统计信息 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '10px', 
        background: 'var(--vscode-input-background)',
        border: '1px solid var(--vscode-input-border)',
        borderRadius: '4px',
        fontSize: '12px',
        color: 'var(--vscode-descriptionForeground)'
      }}>
        <strong>任务统计:</strong> 
        总计 {stats.total} | 
        新建 {stats.new} | 
        进行中 {stats.inProgress} | 
        完成 {stats.completed} | 
        失败 {stats.failed}
      </div>

      {/* 批量操作按钮 */}
      <div style={{ 
        marginBottom: '20px', 
        display: 'flex', 
        gap: '8px', 
        flexWrap: 'wrap' 
      }}>
        <button 
          onClick={handleClearCompleted}
          style={{ 
            padding: '4px 8px', 
            fontSize: '11px',
            background: 'var(--vscode-button-background)',
            color: 'var(--vscode-button-foreground)',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer'
          }}
        >
          清理已完成
        </button>
        <button 
          onClick={handleMarkAllCompleted}
          style={{ 
            padding: '4px 8px', 
            fontSize: '11px',
            background: 'var(--vscode-button-background)',
            color: 'var(--vscode-button-foreground)',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer'
          }}
        >
          全部完成
        </button>
        <button 
          onClick={handleResetFailedTasks}
          style={{ 
            padding: '4px 8px', 
            fontSize: '11px',
            background: 'var(--vscode-button-background)',
            color: 'var(--vscode-button-foreground)',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer'
          }}
        >
          重置失败
        </button>
      </div>

      {/* TodoTaskList 组件 */}
      <TodoTaskList 
        tasks={tasks}
        onTaskAdd={handleTaskAdd}
        onTaskUpdate={handleTaskUpdate}
        onTaskDelete={handleTaskDelete}
      />

      {/* 模拟输入框 */}
      <div style={{
        background: 'var(--vscode-input-background)',
        border: '1px solid var(--vscode-input-border)',
        borderRadius: '4px',
        padding: '12px',
        marginTop: '10px',
        minHeight: '100px',
        color: 'var(--vscode-input-placeholderForeground)',
        opacity: 0.7,
        fontSize: '14px'
      }}>
        这里是模拟的输入框区域...
        <br />
        TodoTaskList 组件已集成在上方
      </div>

      {/* 使用说明 */}
      <div style={{ 
        marginTop: '20px', 
        padding: '10px', 
        background: 'var(--vscode-textBlockQuote-background)',
        border: '1px solid var(--vscode-input-border)',
        borderRadius: '4px',
        fontSize: '11px',
        color: 'var(--vscode-descriptionForeground)',
        lineHeight: '1.4'
      }}>
        <strong>使用说明:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
          <li>点击左上角箭头或右侧按钮展开/收起任务列表</li>
          <li>点击任务状态图标切换状态 (新建→进行中→完成→新建)</li>
          <li>鼠标悬停任务项显示删除按钮</li>
          <li>点击"添加任务"按钮创建新任务</li>
          <li>任务数据自动保存到浏览器本地存储</li>
          <li>使用上方的批量操作按钮管理多个任务</li>
        </ul>
      </div>
    </div>
  )
}

export default TodoTaskListExample
