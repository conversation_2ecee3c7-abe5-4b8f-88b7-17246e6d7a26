import React, { useState } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"

// Task status enum
export enum TaskStatus {
	NEW = "new",
	IN_PROGRESS = "in_progress", 
	COMPLETED = "completed",
	FAILED = "failed"
}

// Task interface
export interface TodoTask {
	id: string
	title: string
	description?: string
	status: TaskStatus
	createdAt: Date
	updatedAt: Date
}

// Styled components
const TodoContainer = styled.div<{ isExpanded: boolean }>`
	position: relative;
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-bottom: ${props => props.isExpanded ? '1px solid var(--vscode-input-border)' : 'none'};
	border-radius: 2px 2px 0 0;
	margin-bottom: 0;
	transition: all 0.2s ease-in-out;
	max-height: ${props => props.isExpanded ? '300px' : '40px'};
	overflow: hidden;
	width: 100%;
	box-sizing: border-box;
`

const TodoHeader = styled.div<{ isExpanded: boolean }>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8px 43px 8px 24px;
	background: var(--vscode-input-background);
	border-bottom: ${props => props.isExpanded ? '1px solid var(--vscode-input-border)' : 'none'};
	cursor: pointer;
	user-select: none;

	&:hover {
		background: var(--vscode-list-hoverBackground);
	}
`

const TodoHeaderLeft = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
`

const TodoTitle = styled.span`
	font-size: 12px;
	font-weight: 500;
	color: var(--vscode-foreground);
`

const TaskCount = styled.span`
	font-size: 11px;
	color: var(--vscode-descriptionForeground);
	background: var(--vscode-badge-background);
	padding: 2px 6px;
	border-radius: 10px;
	min-width: 16px;
	text-align: center;
`

const ToggleButton = styled(VSCodeButton)`
	padding: 2px 4px !important;
	height: 24px !important;
	min-width: 24px !important;
`

const TodoList = styled.div`
	padding: 8px 43px 8px 24px;
	max-height: 240px;
	overflow-y: auto;
`

const AddTaskButton = styled(VSCodeButton)`
	width: 100%;
	margin-bottom: 8px;
	font-size: 11px !important;
`

const TaskItem = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 6px 0;
	border-bottom: 1px solid var(--vscode-widget-border);

	&:last-child {
		border-bottom: none;
	}

	&:hover .task-actions {
		opacity: 1;
	}
`

const TaskIcon = styled.span<{ status: TaskStatus }>`
	width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	flex-shrink: 0;
	
	color: ${props => {
		switch (props.status) {
			case TaskStatus.NEW:
				return 'var(--vscode-charts-blue)';
			case TaskStatus.IN_PROGRESS:
				return 'var(--vscode-charts-yellow)';
			case TaskStatus.COMPLETED:
				return 'var(--vscode-charts-green)';
			case TaskStatus.FAILED:
				return 'var(--vscode-errorForeground)';
			default:
				return 'var(--vscode-foreground)';
		}
	}};
`

const TaskContent = styled.div`
	flex: 1;
	min-width: 0;
`

const TaskTitle = styled.div`
	font-size: 12px;
	color: var(--vscode-foreground);
	font-weight: 500;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
`

const TaskDescription = styled.div`
	font-size: 11px;
	color: var(--vscode-descriptionForeground);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-top: 2px;
`

const TaskActions = styled.div`
	display: flex;
	gap: 4px;
	opacity: 0;
	transition: opacity 0.2s ease-in-out;
`

const TaskActionButton = styled(VSCodeButton)`
	padding: 2px 4px !important;
	height: 20px !important;
	min-width: 20px !important;
`

const EmptyState = styled.div`
	text-align: center;
	padding: 20px;
	color: var(--vscode-descriptionForeground);
	font-size: 11px;
`

// Helper function to get task icon based on status
const getTaskIcon = (status: TaskStatus): string => {
	switch (status) {
		case TaskStatus.NEW:
			return "codicon-circle-outline"
		case TaskStatus.IN_PROGRESS:
			return "codicon-sync"
		case TaskStatus.COMPLETED:
			return "codicon-check"
		case TaskStatus.FAILED:
			return "codicon-error"
		default:
			return "codicon-circle-outline"
	}
}

// Mock data for demonstration
const mockTasks: TodoTask[] = [
	{
		id: "1",
		title: "设计todo组件界面",
		description: "创建可折叠的任务列表组件",
		status: TaskStatus.COMPLETED,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	{
		id: "2", 
		title: "实现任务状态管理",
		description: "添加新建、进行中、完成、失败四种状态",
		status: TaskStatus.IN_PROGRESS,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	{
		id: "3",
		title: "集成到输入框组件",
		description: "将todo列表固定在输入框上方",
		status: TaskStatus.NEW,
		createdAt: new Date(),
		updatedAt: new Date()
	}
]

interface TodoTaskListProps {
	tasks?: TodoTask[]
	onTaskAdd?: (task: Omit<TodoTask, 'id' | 'createdAt' | 'updatedAt'>) => void
	onTaskUpdate?: (taskId: string, updates: Partial<TodoTask>) => void
	onTaskDelete?: (taskId: string) => void
}

const TodoTaskList: React.FC<TodoTaskListProps> = ({
	tasks = mockTasks,
	onTaskAdd,
	onTaskUpdate,
	onTaskDelete
}) => {
	const [isExpanded, setIsExpanded] = useState(false)

	const toggleExpanded = () => {
		setIsExpanded(!isExpanded)
	}

	const handleAddTask = () => {
		const title = prompt("请输入任务标题:")
		if (title && title.trim()) {
			const description = prompt("请输入任务描述 (可选):")
			onTaskAdd?.({
				title: title.trim(),
				description: description?.trim() || undefined,
				status: TaskStatus.NEW
			})
		}
	}

	const handleToggleTaskStatus = (taskId: string, currentStatus: TaskStatus) => {
		let newStatus: TaskStatus
		switch (currentStatus) {
			case TaskStatus.NEW:
				newStatus = TaskStatus.IN_PROGRESS
				break
			case TaskStatus.IN_PROGRESS:
				newStatus = TaskStatus.COMPLETED
				break
			case TaskStatus.COMPLETED:
				newStatus = TaskStatus.NEW
				break
			case TaskStatus.FAILED:
				newStatus = TaskStatus.NEW
				break
			default:
				newStatus = TaskStatus.NEW
		}
		onTaskUpdate?.(taskId, { status: newStatus, updatedAt: new Date() })
	}

	const handleDeleteTask = (taskId: string) => {
		if (confirm("确定要删除这个任务吗？")) {
			onTaskDelete?.(taskId)
		}
	}
	
	const taskCounts = tasks.reduce((counts, task) => {
		counts[task.status] = (counts[task.status] || 0) + 1
		return counts
	}, {} as Record<TaskStatus, number>)
	
	const totalTasks = tasks.length
	const completedTasks = taskCounts[TaskStatus.COMPLETED] || 0
	
	return (
		<TodoContainer isExpanded={isExpanded}>
			<TodoHeader onClick={toggleExpanded} isExpanded={isExpanded}>
				<TodoHeaderLeft>
					<span className={`codicon ${isExpanded ? 'codicon-chevron-down' : 'codicon-chevron-right'}`} 
						  style={{ fontSize: '12px' }} />
					<TodoTitle>待办任务</TodoTitle>
					<TaskCount>{completedTasks}/{totalTasks}</TaskCount>
				</TodoHeaderLeft>
				<ToggleButton appearance="icon" aria-label={isExpanded ? "收起任务列表" : "展开任务列表"}>
					<span className={`codicon ${isExpanded ? 'codicon-chevron-up' : 'codicon-chevron-down'}`} 
						  style={{ fontSize: '12px' }} />
				</ToggleButton>
			</TodoHeader>
			
			{isExpanded && (
				<TodoList>
					<AddTaskButton
						appearance="secondary"
						onClick={handleAddTask}
						disabled={!onTaskAdd}
					>
						<span className="codicon codicon-add" style={{ marginRight: '4px' }} />
						添加任务
					</AddTaskButton>

					{tasks.length === 0 ? (
						<EmptyState>暂无任务</EmptyState>
					) : (
						tasks.map(task => (
							<TaskItem key={task.id}>
								<TaskIcon
									status={task.status}
									onClick={() => handleToggleTaskStatus(task.id, task.status)}
									style={{ cursor: onTaskUpdate ? 'pointer' : 'default' }}
								>
									<span className={`codicon ${getTaskIcon(task.status)}`} />
								</TaskIcon>
								<TaskContent>
									<TaskTitle>{task.title}</TaskTitle>
									{task.description && (
										<TaskDescription>{task.description}</TaskDescription>
									)}
								</TaskContent>
								<TaskActions className="task-actions">
									<TaskActionButton
										appearance="icon"
										onClick={() => handleDeleteTask(task.id)}
										disabled={!onTaskDelete}
										title="删除任务"
									>
										<span className="codicon codicon-trash" style={{ fontSize: '10px' }} />
									</TaskActionButton>
								</TaskActions>
							</TaskItem>
						))
					)}
				</TodoList>
			)}
		</TodoContainer>
	)
}

export default TodoTaskList
