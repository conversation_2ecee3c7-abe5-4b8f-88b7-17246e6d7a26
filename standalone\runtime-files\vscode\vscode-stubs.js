// GENERATED CODE -- DO NOT EDIT!
console.log("Loading stubs...")
const { createStub } = require("./stub-utils")
vscode = {}
vscode.version = createStub("vscode.version")
vscode.Position = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Position(", args, ")")
		return createStub(vscode.Position)
	}
}
vscode.Range = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Range(", args, ")")
		return createStub(vscode.Range)
	}
}
vscode.Selection = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Selection(", args, ")")
		return createStub(vscode.Selection)
	}
}
vscode.TextEditorSelectionChangeKind = { Keyboard: 0, Mouse: 0, Command: 0 }
vscode.TextEditorCursorStyle = { Line: 0, Block: 0, Underline: 0, LineThin: 0, BlockOutline: 0, UnderlineThin: 0 }
vscode.TextEditorLineNumbersStyle = { Off: 0, On: 0, Relative: 0 }
vscode.TextEditorRevealType = { Default: 0, InCenter: 0, InCenterIfOutsideViewport: 0, AtTop: 0 }
vscode.OverviewRulerLane = { Left: 0, Center: 0, Right: 0, Full: 0 }
vscode.DecorationRangeBehavior = { OpenOpen: 0, ClosedClosed: 0, OpenClosed: 0, ClosedOpen: 0 }
vscode.ThemeColor = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ThemeColor(", args, ")")
		return createStub(vscode.ThemeColor)
	}
}
vscode.ThemeIcon = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ThemeIcon(", args, ")")
		return createStub(vscode.ThemeIcon)
	}
}
vscode.EndOfLine = { LF: 0, CRLF: 0 }
vscode.Uri = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Uri(", args, ")")
		return createStub(vscode.Uri)
	}
}
vscode.CancellationTokenSource = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CancellationTokenSource(", args, ")")
		return createStub(vscode.CancellationTokenSource)
	}
}
vscode.CancellationError = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CancellationError(", args, ")")
		return createStub(vscode.CancellationError)
	}
}
vscode.Disposable = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Disposable(", args, ")")
		return createStub(vscode.Disposable)
	}
}
vscode.EventEmitter = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.EventEmitter(", args, ")")
		return createStub(vscode.EventEmitter)
	}
}
vscode.QuickPickItemKind = { Separator: 0, Default: 0 }
vscode.InputBoxValidationSeverity = { Info: 0, Warning: 0, Error: 0 }
vscode.RelativePattern = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.RelativePattern(", args, ")")
		return createStub(vscode.RelativePattern)
	}
}
vscode.CodeActionKind = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CodeActionKind(", args, ")")
		return createStub(vscode.CodeActionKind)
	}
}
vscode.CodeActionTriggerKind = { Invoke: 0, Automatic: 0 }
vscode.CodeAction = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CodeAction(", args, ")")
		return createStub(vscode.CodeAction)
	}
}
vscode.CodeLens = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CodeLens(", args, ")")
		return createStub(vscode.CodeLens)
	}
}
vscode.MarkdownString = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.MarkdownString(", args, ")")
		return createStub(vscode.MarkdownString)
	}
}
vscode.Hover = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Hover(", args, ")")
		return createStub(vscode.Hover)
	}
}
vscode.EvaluatableExpression = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.EvaluatableExpression(", args, ")")
		return createStub(vscode.EvaluatableExpression)
	}
}
vscode.InlineValueText = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlineValueText(", args, ")")
		return createStub(vscode.InlineValueText)
	}
}
vscode.InlineValueVariableLookup = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlineValueVariableLookup(", args, ")")
		return createStub(vscode.InlineValueVariableLookup)
	}
}
vscode.InlineValueEvaluatableExpression = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlineValueEvaluatableExpression(", args, ")")
		return createStub(vscode.InlineValueEvaluatableExpression)
	}
}
vscode.DocumentHighlightKind = { Text: 0, Read: 0, Write: 0 }
vscode.DocumentHighlight = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DocumentHighlight(", args, ")")
		return createStub(vscode.DocumentHighlight)
	}
}
vscode.SymbolKind = {
	File: 0,
	Module: 0,
	Namespace: 0,
	Package: 0,
	Class: 0,
	Method: 0,
	Property: 0,
	Field: 0,
	Constructor: 0,
	Enum: 0,
	Interface: 0,
	Function: 0,
	Variable: 0,
	Constant: 0,
	String: 0,
	Number: 0,
	Boolean: 0,
	Array: 0,
	Object: 0,
	Key: 0,
	Null: 0,
	EnumMember: 0,
	Struct: 0,
	Event: 0,
	Operator: 0,
	TypeParameter: 0,
}
vscode.SymbolTag = { Deprecated: 0 }
vscode.SymbolInformation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SymbolInformation(", args, ")")
		return createStub(vscode.SymbolInformation)
	}
}
vscode.DocumentSymbol = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DocumentSymbol(", args, ")")
		return createStub(vscode.DocumentSymbol)
	}
}
vscode.TextEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TextEdit(", args, ")")
		return createStub(vscode.TextEdit)
	}
}
vscode.SnippetTextEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SnippetTextEdit(", args, ")")
		return createStub(vscode.SnippetTextEdit)
	}
}
vscode.NotebookEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookEdit(", args, ")")
		return createStub(vscode.NotebookEdit)
	}
}
vscode.WorkspaceEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.WorkspaceEdit(", args, ")")
		return createStub(vscode.WorkspaceEdit)
	}
}
vscode.SnippetString = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SnippetString(", args, ")")
		return createStub(vscode.SnippetString)
	}
}
vscode.SemanticTokensLegend = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SemanticTokensLegend(", args, ")")
		return createStub(vscode.SemanticTokensLegend)
	}
}
vscode.SemanticTokensBuilder = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SemanticTokensBuilder(", args, ")")
		return createStub(vscode.SemanticTokensBuilder)
	}
}
vscode.SemanticTokens = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SemanticTokens(", args, ")")
		return createStub(vscode.SemanticTokens)
	}
}
vscode.SemanticTokensEdits = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SemanticTokensEdits(", args, ")")
		return createStub(vscode.SemanticTokensEdits)
	}
}
vscode.SemanticTokensEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SemanticTokensEdit(", args, ")")
		return createStub(vscode.SemanticTokensEdit)
	}
}
vscode.ParameterInformation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ParameterInformation(", args, ")")
		return createStub(vscode.ParameterInformation)
	}
}
vscode.SignatureInformation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SignatureInformation(", args, ")")
		return createStub(vscode.SignatureInformation)
	}
}
vscode.SignatureHelp = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SignatureHelp(", args, ")")
		return createStub(vscode.SignatureHelp)
	}
}
vscode.SignatureHelpTriggerKind = { Invoke: 0, TriggerCharacter: 0, ContentChange: 0 }
vscode.CompletionItemKind = {
	Text: 0,
	Method: 0,
	Function: 0,
	Constructor: 0,
	Field: 0,
	Variable: 0,
	Class: 0,
	Interface: 0,
	Module: 0,
	Property: 0,
	Unit: 0,
	Value: 0,
	Enum: 0,
	Keyword: 0,
	Snippet: 0,
	Color: 0,
	Reference: 0,
	File: 0,
	Folder: 0,
	EnumMember: 0,
	Constant: 0,
	Struct: 0,
	Event: 0,
	Operator: 0,
	TypeParameter: 0,
	User: 0,
	Issue: 0,
}
vscode.CompletionItemTag = { Deprecated: 0 }
vscode.CompletionItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CompletionItem(", args, ")")
		return createStub(vscode.CompletionItem)
	}
}
vscode.CompletionList = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CompletionList(", args, ")")
		return createStub(vscode.CompletionList)
	}
}
vscode.CompletionTriggerKind = { Invoke: 0, TriggerCharacter: 0, TriggerForIncompleteCompletions: 0 }
vscode.InlineCompletionList = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlineCompletionList(", args, ")")
		return createStub(vscode.InlineCompletionList)
	}
}
vscode.InlineCompletionTriggerKind = { Invoke: 0, Automatic: 0 }
vscode.InlineCompletionItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlineCompletionItem(", args, ")")
		return createStub(vscode.InlineCompletionItem)
	}
}
vscode.DocumentLink = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DocumentLink(", args, ")")
		return createStub(vscode.DocumentLink)
	}
}
vscode.Color = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Color(", args, ")")
		return createStub(vscode.Color)
	}
}
vscode.ColorInformation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ColorInformation(", args, ")")
		return createStub(vscode.ColorInformation)
	}
}
vscode.ColorPresentation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ColorPresentation(", args, ")")
		return createStub(vscode.ColorPresentation)
	}
}
vscode.InlayHintKind = { Type: 0, Parameter: 0 }
vscode.InlayHintLabelPart = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlayHintLabelPart(", args, ")")
		return createStub(vscode.InlayHintLabelPart)
	}
}
vscode.InlayHint = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.InlayHint(", args, ")")
		return createStub(vscode.InlayHint)
	}
}
vscode.FoldingRange = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.FoldingRange(", args, ")")
		return createStub(vscode.FoldingRange)
	}
}
vscode.FoldingRangeKind = { Comment: 0, Imports: 0, Region: 0 }
vscode.SelectionRange = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SelectionRange(", args, ")")
		return createStub(vscode.SelectionRange)
	}
}
vscode.CallHierarchyItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CallHierarchyItem(", args, ")")
		return createStub(vscode.CallHierarchyItem)
	}
}
vscode.CallHierarchyIncomingCall = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CallHierarchyIncomingCall(", args, ")")
		return createStub(vscode.CallHierarchyIncomingCall)
	}
}
vscode.CallHierarchyOutgoingCall = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CallHierarchyOutgoingCall(", args, ")")
		return createStub(vscode.CallHierarchyOutgoingCall)
	}
}
vscode.TypeHierarchyItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TypeHierarchyItem(", args, ")")
		return createStub(vscode.TypeHierarchyItem)
	}
}
vscode.LinkedEditingRanges = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.LinkedEditingRanges(", args, ")")
		return createStub(vscode.LinkedEditingRanges)
	}
}
vscode.DocumentDropEdit = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DocumentDropEdit(", args, ")")
		return createStub(vscode.DocumentDropEdit)
	}
}
vscode.IndentAction = { None: 0, Indent: 0, IndentOutdent: 0, Outdent: 0 }
vscode.SyntaxTokenType = { Other: 0, Comment: 0, String: 0, RegEx: 0 }
vscode.ConfigurationTarget = { Global: 0, Workspace: 0, WorkspaceFolder: 0 }
vscode.Location = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Location(", args, ")")
		return createStub(vscode.Location)
	}
}
vscode.DiagnosticSeverity = { Error: 0, Warning: 0, Information: 0, Hint: 0 }
vscode.DiagnosticRelatedInformation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DiagnosticRelatedInformation(", args, ")")
		return createStub(vscode.DiagnosticRelatedInformation)
	}
}
vscode.DiagnosticTag = { Unnecessary: 0, Deprecated: 0 }
vscode.Diagnostic = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Diagnostic(", args, ")")
		return createStub(vscode.Diagnostic)
	}
}
vscode.LanguageStatusSeverity = { Information: 0, Warning: 0, Error: 0 }
vscode.ViewColumn = { Active: 0, Beside: 0, One: 0, Two: 0, Three: 0, Four: 0, Five: 0, Six: 0, Seven: 0, Eight: 0, Nine: 0 }
vscode.StatusBarAlignment = { Left: 0, Right: 0 }
vscode.TerminalLocation = { Panel: 0, Editor: 0 }
vscode.TerminalLink = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TerminalLink(", args, ")")
		return createStub(vscode.TerminalLink)
	}
}
vscode.TerminalProfile = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TerminalProfile(", args, ")")
		return createStub(vscode.TerminalProfile)
	}
}
vscode.FileDecoration = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.FileDecoration(", args, ")")
		return createStub(vscode.FileDecoration)
	}
}
vscode.ExtensionKind = { UI: 0, Workspace: 0 }
vscode.ExtensionMode = { Production: 0, Development: 0, Test: 0 }
vscode.ColorThemeKind = { Light: 0, Dark: 0, HighContrast: 0, HighContrastLight: 0 }
vscode.TaskRevealKind = { Always: 0, Silent: 0, Never: 0 }
vscode.TaskPanelKind = { Shared: 0, Dedicated: 0, New: 0 }
vscode.TaskGroup = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TaskGroup(", args, ")")
		return createStub(vscode.TaskGroup)
	}
}
vscode.ProcessExecution = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ProcessExecution(", args, ")")
		return createStub(vscode.ProcessExecution)
	}
}
vscode.ShellQuoting = { Escape: 0, Strong: 0, Weak: 0 }
vscode.ShellExecution = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.ShellExecution(", args, ")")
		return createStub(vscode.ShellExecution)
	}
}
vscode.CustomExecution = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.CustomExecution(", args, ")")
		return createStub(vscode.CustomExecution)
	}
}
vscode.TaskScope = { Global: 0, Workspace: 0 }
vscode.Task = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Task(", args, ")")
		return createStub(vscode.Task)
	}
}
vscode.tasks = {}
vscode.tasks.registerTaskProvider = function (type, provider) {
	console.log("Called stubbed function: vscode.tasks.registerTaskProvider")
	return createStub("unknown")
}
vscode.tasks.fetchTasks = function (filter) {
	console.log("Called stubbed function: vscode.tasks.fetchTasks")
	return []
}
vscode.tasks.executeTask = function (task) {
	console.log("Called stubbed function: vscode.tasks.executeTask")
	return Promise.resolve(null)
}
vscode.tasks.taskExecutions = createStub("vscode.tasks.taskExecutions")
vscode.tasks.onDidStartTask = createStub("vscode.tasks.onDidStartTask")
vscode.tasks.onDidEndTask = createStub("vscode.tasks.onDidEndTask")
vscode.tasks.onDidStartTaskProcess = createStub("vscode.tasks.onDidStartTaskProcess")
vscode.tasks.onDidEndTaskProcess = createStub("vscode.tasks.onDidEndTaskProcess")
vscode.FileType = { Unknown: 0, File: 0, Directory: 0, SymbolicLink: 0 }
vscode.FilePermission = { Readonly: 0 }
vscode.FileSystemError = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.FileSystemError(", args, ")")
		return createStub(vscode.FileSystemError)
	}
}
vscode.FileChangeType = { Changed: 0, Created: 0, Deleted: 0 }
vscode.UIKind = { Desktop: 0, Web: 0 }
vscode.LogLevel = { Off: 0, Trace: 0, Debug: 0, Info: 0, Warning: 0, Error: 0 }
vscode.env = {}
vscode.env.appName = createStub("vscode.env.appName")
vscode.env.appRoot = createStub("vscode.env.appRoot")
vscode.env.appHost = createStub("vscode.env.appHost")
vscode.env.uriScheme = createStub("vscode.env.uriScheme")
vscode.env.language = createStub("vscode.env.language")
vscode.env.clipboard = createStub("vscode.env.clipboard")
vscode.env.machineId = createStub("vscode.env.machineId")
vscode.env.sessionId = createStub("vscode.env.sessionId")
vscode.env.isNewAppInstall = createStub("vscode.env.isNewAppInstall")
vscode.env.isTelemetryEnabled = createStub("vscode.env.isTelemetryEnabled")
vscode.env.onDidChangeTelemetryEnabled = createStub("vscode.env.onDidChangeTelemetryEnabled")
vscode.env.onDidChangeShell = createStub("vscode.env.onDidChangeShell")
vscode.env.createTelemetryLogger = function (sender, options) {
	console.log("Called stubbed function: vscode.env.createTelemetryLogger")
	return createStub("unknown")
}
vscode.env.remoteName = createStub("vscode.env.remoteName")
vscode.env.shell = createStub("vscode.env.shell")
vscode.env.uiKind = createStub("vscode.env.uiKind")
vscode.env.openExternal = function (target) {
	console.log("Called stubbed function: vscode.env.openExternal")
	return false
}
vscode.env.asExternalUri = function (target) {
	console.log("Called stubbed function: vscode.env.asExternalUri")
	return Promise.resolve(null)
}
vscode.env.logLevel = createStub("vscode.env.logLevel")
vscode.env.onDidChangeLogLevel = createStub("vscode.env.onDidChangeLogLevel")
vscode.commands = {}
vscode.commands.registerCommand = function (command, callback, thisArg) {
	console.log("Called stubbed function: vscode.commands.registerCommand")
	return createStub("unknown")
}
vscode.commands.registerTextEditorCommand = function (command, callback, thisArg) {
	console.log("Called stubbed function: vscode.commands.registerTextEditorCommand")
	return createStub("unknown")
}
vscode.commands.executeCommand = function (command, rest) {
	console.log("Called stubbed function: vscode.commands.executeCommand")
	return Promise.resolve(null)
}
vscode.commands.getCommands = function (filterInternal) {
	console.log("Called stubbed function: vscode.commands.getCommands")
	return ""
}
vscode.window = {}
vscode.window.tabGroups = createStub("vscode.window.tabGroups")
vscode.window.activeTextEditor = createStub("vscode.window.activeTextEditor")
vscode.window.visibleTextEditors = createStub("vscode.window.visibleTextEditors")
vscode.window.onDidChangeActiveTextEditor = createStub("vscode.window.onDidChangeActiveTextEditor")
vscode.window.onDidChangeVisibleTextEditors = createStub("vscode.window.onDidChangeVisibleTextEditors")
vscode.window.onDidChangeTextEditorSelection = createStub("vscode.window.onDidChangeTextEditorSelection")
vscode.window.onDidChangeTextEditorVisibleRanges = createStub("vscode.window.onDidChangeTextEditorVisibleRanges")
vscode.window.onDidChangeTextEditorOptions = createStub("vscode.window.onDidChangeTextEditorOptions")
vscode.window.onDidChangeTextEditorViewColumn = createStub("vscode.window.onDidChangeTextEditorViewColumn")
vscode.window.visibleNotebookEditors = createStub("vscode.window.visibleNotebookEditors")
vscode.window.onDidChangeVisibleNotebookEditors = createStub("vscode.window.onDidChangeVisibleNotebookEditors")
vscode.window.activeNotebookEditor = createStub("vscode.window.activeNotebookEditor")
vscode.window.onDidChangeActiveNotebookEditor = createStub("vscode.window.onDidChangeActiveNotebookEditor")
vscode.window.onDidChangeNotebookEditorSelection = createStub("vscode.window.onDidChangeNotebookEditorSelection")
vscode.window.onDidChangeNotebookEditorVisibleRanges = createStub("vscode.window.onDidChangeNotebookEditorVisibleRanges")
vscode.window.terminals = createStub("vscode.window.terminals")
vscode.window.activeTerminal = createStub("vscode.window.activeTerminal")
vscode.window.onDidChangeActiveTerminal = createStub("vscode.window.onDidChangeActiveTerminal")
vscode.window.onDidOpenTerminal = createStub("vscode.window.onDidOpenTerminal")
vscode.window.onDidCloseTerminal = createStub("vscode.window.onDidCloseTerminal")
vscode.window.onDidChangeTerminalState = createStub("vscode.window.onDidChangeTerminalState")
vscode.window.state = createStub("vscode.window.state")
vscode.window.onDidChangeWindowState = createStub("vscode.window.onDidChangeWindowState")
vscode.window.showTextDocument = function (document, column, preserveFocus) {
	console.log("Called stubbed function: vscode.window.showTextDocument")
	return Promise.resolve(null)
}
vscode.window.showTextDocument = function (document, options) {
	console.log("Called stubbed function: vscode.window.showTextDocument")
	return Promise.resolve(null)
}
vscode.window.showTextDocument = function (uri, options) {
	console.log("Called stubbed function: vscode.window.showTextDocument")
	return Promise.resolve(null)
}
vscode.window.showNotebookDocument = function (document, options) {
	console.log("Called stubbed function: vscode.window.showNotebookDocument")
	return Promise.resolve(null)
}
vscode.window.createTextEditorDecorationType = function (options) {
	console.log("Called stubbed function: vscode.window.createTextEditorDecorationType")
	return createStub("unknown")
}
vscode.window.showInformationMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showInformationMessage")
	return Promise.resolve(null)
}
vscode.window.showInformationMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showInformationMessage")
	return Promise.resolve(null)
}
vscode.window.showInformationMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showInformationMessage")
	return Promise.resolve(null)
}
vscode.window.showInformationMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showInformationMessage")
	return Promise.resolve(null)
}
vscode.window.showWarningMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showWarningMessage")
	return Promise.resolve(null)
}
vscode.window.showWarningMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showWarningMessage")
	return Promise.resolve(null)
}
vscode.window.showWarningMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showWarningMessage")
	return Promise.resolve(null)
}
vscode.window.showWarningMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showWarningMessage")
	return Promise.resolve(null)
}
vscode.window.showErrorMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showErrorMessage")
	return Promise.resolve(null)
}
vscode.window.showErrorMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showErrorMessage")
	return Promise.resolve(null)
}
vscode.window.showErrorMessage = function (message, items) {
	console.log("Called stubbed function: vscode.window.showErrorMessage")
	return Promise.resolve(null)
}
vscode.window.showErrorMessage = function (message, options, items) {
	console.log("Called stubbed function: vscode.window.showErrorMessage")
	return Promise.resolve(null)
}
vscode.window.showQuickPick = function (items, options, token) {
	console.log("Called stubbed function: vscode.window.showQuickPick")
	return ""
}
vscode.window.showQuickPick = function (items, options, token) {
	console.log("Called stubbed function: vscode.window.showQuickPick")
	return ""
}
vscode.window.showQuickPick = function (items, options, token) {
	console.log("Called stubbed function: vscode.window.showQuickPick")
	return []
}
vscode.window.showQuickPick = function (items, options, token) {
	console.log("Called stubbed function: vscode.window.showQuickPick")
	return Promise.resolve(null)
}
vscode.window.showWorkspaceFolderPick = function (options) {
	console.log("Called stubbed function: vscode.window.showWorkspaceFolderPick")
	return Promise.resolve(null)
}
vscode.window.showSaveDialog = function (options) {
	console.log("Called stubbed function: vscode.window.showSaveDialog")
	return Promise.resolve(null)
}
vscode.window.showInputBox = function (options, token) {
	console.log("Called stubbed function: vscode.window.showInputBox")
	return ""
}
vscode.window.createQuickPick = function () {
	console.log("Called stubbed function: vscode.window.createQuickPick")
	return createStub("unknown")
}
vscode.window.createInputBox = function () {
	console.log("Called stubbed function: vscode.window.createInputBox")
	return createStub("unknown")
}
vscode.window.createOutputChannel = function (name, languageId) {
	console.log("Called stubbed function: vscode.window.createOutputChannel")
	return createStub("unknown")
}
vscode.window.createOutputChannel = function (name, options) {
	console.log("Called stubbed function: vscode.window.createOutputChannel")
	return createStub("unknown")
}
vscode.window.createWebviewPanel = function (viewType, title, showOptions, options) {
	console.log("Called stubbed function: vscode.window.createWebviewPanel")
	return createStub("unknown")
}
vscode.window.setStatusBarMessage = function (text, hideAfterTimeout) {
	console.log("Called stubbed function: vscode.window.setStatusBarMessage")
	return createStub("unknown")
}
vscode.window.setStatusBarMessage = function (text, hideWhenDone) {
	console.log("Called stubbed function: vscode.window.setStatusBarMessage")
	return createStub("unknown")
}
vscode.window.setStatusBarMessage = function (text) {
	console.log("Called stubbed function: vscode.window.setStatusBarMessage")
	return createStub("unknown")
}
vscode.window.withScmProgress = function (task) {
	console.log("Called stubbed function: vscode.window.withScmProgress")
	return Promise.resolve(null)
}
vscode.window.withProgress = function (options, task) {
	console.log("Called stubbed function: vscode.window.withProgress")
	return Promise.resolve(null)
}
vscode.window.createStatusBarItem = function (id, alignment, priority) {
	console.log("Called stubbed function: vscode.window.createStatusBarItem")
	return createStub("unknown")
}
vscode.window.createStatusBarItem = function (alignment, priority) {
	console.log("Called stubbed function: vscode.window.createStatusBarItem")
	return createStub("unknown")
}
vscode.window.createTerminal = function (name, shellPath, shellArgs) {
	console.log("Called stubbed function: vscode.window.createTerminal")
	return createStub("unknown")
}
vscode.window.createTerminal = function (options) {
	console.log("Called stubbed function: vscode.window.createTerminal")
	return createStub("unknown")
}
vscode.window.createTerminal = function (options) {
	console.log("Called stubbed function: vscode.window.createTerminal")
	return createStub("unknown")
}
vscode.window.registerTreeDataProvider = function (viewId, treeDataProvider) {
	console.log("Called stubbed function: vscode.window.registerTreeDataProvider")
	return createStub("unknown")
}
vscode.window.createTreeView = function (viewId, options) {
	console.log("Called stubbed function: vscode.window.createTreeView")
	return createStub("unknown")
}
vscode.window.registerUriHandler = function (handler) {
	console.log("Called stubbed function: vscode.window.registerUriHandler")
	return createStub("unknown")
}
vscode.window.registerWebviewPanelSerializer = function (viewType, serializer) {
	console.log("Called stubbed function: vscode.window.registerWebviewPanelSerializer")
	return createStub("unknown")
}
vscode.window.registerWebviewViewProvider = function (viewId, provider, options) {
	console.log("Called stubbed function: vscode.window.registerWebviewViewProvider")
	return createStub("unknown")
}
vscode.window.registerCustomEditorProvider = function (viewType, provider, options) {
	console.log("Called stubbed function: vscode.window.registerCustomEditorProvider")
	return createStub("unknown")
}
vscode.window.registerTerminalLinkProvider = function (provider) {
	console.log("Called stubbed function: vscode.window.registerTerminalLinkProvider")
	return createStub("unknown")
}
vscode.window.registerTerminalProfileProvider = function (id, provider) {
	console.log("Called stubbed function: vscode.window.registerTerminalProfileProvider")
	return createStub("unknown")
}
vscode.window.registerFileDecorationProvider = function (provider) {
	console.log("Called stubbed function: vscode.window.registerFileDecorationProvider")
	return createStub("unknown")
}
vscode.window.activeColorTheme = createStub("vscode.window.activeColorTheme")
vscode.window.onDidChangeActiveColorTheme = createStub("vscode.window.onDidChangeActiveColorTheme")
vscode.DataTransferItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DataTransferItem(", args, ")")
		return createStub(vscode.DataTransferItem)
	}
}
vscode.DataTransfer = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DataTransfer(", args, ")")
		return createStub(vscode.DataTransfer)
	}
}
vscode.TreeItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TreeItem(", args, ")")
		return createStub(vscode.TreeItem)
	}
}
vscode.TreeItemCollapsibleState = { None: 0, Collapsed: 0, Expanded: 0 }
vscode.TreeItemCheckboxState = { Unchecked: 0, Checked: 0 }
vscode.TerminalExitReason = { Unknown: 0, Shutdown: 0, Process: 0, User: 0, Extension: 0 }
vscode.EnvironmentVariableMutatorType = { Replace: 0, Append: 0, Prepend: 0 }
vscode.ProgressLocation = { SourceControl: 0, Window: 0, Notification: 0 }
vscode.QuickInputButtons = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.QuickInputButtons(", args, ")")
		return createStub(vscode.QuickInputButtons)
	}
}
vscode.TextDocumentChangeReason = { Undo: 0, Redo: 0 }
vscode.TextDocumentSaveReason = { Manual: 0, AfterDelay: 0, FocusOut: 0 }
vscode.workspace = {}
vscode.workspace.fs = createStub("vscode.workspace.fs")
vscode.workspace.rootPath = createStub("vscode.workspace.rootPath")
vscode.workspace.name = createStub("vscode.workspace.name")
vscode.workspace.workspaceFile = createStub("vscode.workspace.workspaceFile")
vscode.workspace.asRelativePath = function (pathOrUri, includeWorkspaceFolder) {
	console.log("Called stubbed function: vscode.workspace.asRelativePath")
	return ""
}
vscode.workspace.createFileSystemWatcher = function (globPattern, ignoreCreateEvents, ignoreChangeEvents, ignoreDeleteEvents) {
	console.log("Called stubbed function: vscode.workspace.createFileSystemWatcher")
	return createStub("unknown")
}
vscode.workspace.findFiles = function (include, exclude, maxResults, token) {
	console.log("Called stubbed function: vscode.workspace.findFiles")
	return []
}
vscode.workspace.saveAll = function (includeUntitled) {
	console.log("Called stubbed function: vscode.workspace.saveAll")
	return false
}
vscode.workspace.applyEdit = function (edit, metadata) {
	console.log("Called stubbed function: vscode.workspace.applyEdit")
	return false
}
vscode.workspace.textDocuments = createStub("vscode.workspace.textDocuments")
vscode.workspace.openTextDocument = function (uri) {
	console.log("Called stubbed function: vscode.workspace.openTextDocument")
	return Promise.resolve(null)
}
vscode.workspace.openTextDocument = function (fileName) {
	console.log("Called stubbed function: vscode.workspace.openTextDocument")
	return Promise.resolve(null)
}
vscode.workspace.openTextDocument = function (options) {
	console.log("Called stubbed function: vscode.workspace.openTextDocument")
	return Promise.resolve(null)
}
vscode.workspace.registerTextDocumentContentProvider = function (scheme, provider) {
	console.log("Called stubbed function: vscode.workspace.registerTextDocumentContentProvider")
	return createStub("unknown")
}
vscode.workspace.onDidOpenTextDocument = createStub("vscode.workspace.onDidOpenTextDocument")
vscode.workspace.onDidCloseTextDocument = createStub("vscode.workspace.onDidCloseTextDocument")
vscode.workspace.onDidChangeTextDocument = createStub("vscode.workspace.onDidChangeTextDocument")
vscode.workspace.onWillSaveTextDocument = createStub("vscode.workspace.onWillSaveTextDocument")
vscode.workspace.onDidSaveTextDocument = createStub("vscode.workspace.onDidSaveTextDocument")
vscode.workspace.notebookDocuments = createStub("vscode.workspace.notebookDocuments")
vscode.workspace.openNotebookDocument = function (uri) {
	console.log("Called stubbed function: vscode.workspace.openNotebookDocument")
	return Promise.resolve(null)
}
vscode.workspace.openNotebookDocument = function (notebookType, content) {
	console.log("Called stubbed function: vscode.workspace.openNotebookDocument")
	return Promise.resolve(null)
}
vscode.workspace.onDidChangeNotebookDocument = createStub("vscode.workspace.onDidChangeNotebookDocument")
vscode.workspace.onWillSaveNotebookDocument = createStub("vscode.workspace.onWillSaveNotebookDocument")
vscode.workspace.onDidSaveNotebookDocument = createStub("vscode.workspace.onDidSaveNotebookDocument")
vscode.workspace.registerNotebookSerializer = function (notebookType, serializer, options) {
	console.log("Called stubbed function: vscode.workspace.registerNotebookSerializer")
	return createStub("unknown")
}
vscode.workspace.onDidOpenNotebookDocument = createStub("vscode.workspace.onDidOpenNotebookDocument")
vscode.workspace.onDidCloseNotebookDocument = createStub("vscode.workspace.onDidCloseNotebookDocument")
vscode.workspace.onWillCreateFiles = createStub("vscode.workspace.onWillCreateFiles")
vscode.workspace.onDidCreateFiles = createStub("vscode.workspace.onDidCreateFiles")
vscode.workspace.onWillDeleteFiles = createStub("vscode.workspace.onWillDeleteFiles")
vscode.workspace.onDidDeleteFiles = createStub("vscode.workspace.onDidDeleteFiles")
vscode.workspace.onWillRenameFiles = createStub("vscode.workspace.onWillRenameFiles")
vscode.workspace.onDidRenameFiles = createStub("vscode.workspace.onDidRenameFiles")

const workspaceConfigStore = {}
vscode.workspace.getConfiguration = function (section) {
	return {
		get: (key, defaultValue) => {
			return workspaceConfigStore[`${section}.${key}`] ?? defaultValue
		},
		update: (key, value, global) => {
			workspaceConfigStore[`${section}.${key}`] = value
		},
		has: (key) => {
			return `${section}.${key}` in workspaceConfigStore
		},
	}
}

vscode.workspace.onDidChangeConfiguration = createStub("vscode.workspace.onDidChangeConfiguration")
vscode.workspace.registerTaskProvider = function (type, provider) {
	console.log("Called stubbed function: vscode.workspace.registerTaskProvider")
	return createStub("unknown")
}
vscode.workspace.registerFileSystemProvider = function (scheme, provider, options) {
	console.log("Called stubbed function: vscode.workspace.registerFileSystemProvider")
	return createStub("unknown")
}
vscode.workspace.isTrusted = createStub("vscode.workspace.isTrusted")
vscode.workspace.onDidGrantWorkspaceTrust = createStub("vscode.workspace.onDidGrantWorkspaceTrust")
vscode.languages = {}
vscode.languages.getLanguages = function () {
	console.log("Called stubbed function: vscode.languages.getLanguages")
	return ""
}
vscode.languages.setTextDocumentLanguage = function (document, languageId) {
	console.log("Called stubbed function: vscode.languages.setTextDocumentLanguage")
	return Promise.resolve(null)
}
vscode.languages.match = function (selector, document) {
	console.log("Called stubbed function: vscode.languages.match")
	return 0
}
vscode.languages.onDidChangeDiagnostics = createStub("vscode.languages.onDidChangeDiagnostics")
vscode.languages.getDiagnostics = function (resource) {
	console.log("Called stubbed function: vscode.languages.getDiagnostics")
	return []
}
vscode.languages.getDiagnostics = function () {
	console.log("Called stubbed function: vscode.languages.getDiagnostics")
	return []
}
vscode.languages.createDiagnosticCollection = function (name) {
	console.log("Called stubbed function: vscode.languages.createDiagnosticCollection")
	return createStub("unknown")
}
vscode.languages.createLanguageStatusItem = function (id, selector) {
	console.log("Called stubbed function: vscode.languages.createLanguageStatusItem")
	return createStub("unknown")
}
vscode.languages.registerCompletionItemProvider = function (selector, provider, triggerCharacters) {
	console.log("Called stubbed function: vscode.languages.registerCompletionItemProvider")
	return createStub("unknown")
}
vscode.languages.registerInlineCompletionItemProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerInlineCompletionItemProvider")
	return createStub("unknown")
}
vscode.languages.registerCodeActionsProvider = function (selector, provider, metadata) {
	console.log("Called stubbed function: vscode.languages.registerCodeActionsProvider")
	return createStub("unknown")
}
vscode.languages.registerCodeLensProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerCodeLensProvider")
	return createStub("unknown")
}
vscode.languages.registerDefinitionProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDefinitionProvider")
	return createStub("unknown")
}
vscode.languages.registerImplementationProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerImplementationProvider")
	return createStub("unknown")
}
vscode.languages.registerTypeDefinitionProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerTypeDefinitionProvider")
	return createStub("unknown")
}
vscode.languages.registerDeclarationProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDeclarationProvider")
	return createStub("unknown")
}
vscode.languages.registerHoverProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerHoverProvider")
	return createStub("unknown")
}
vscode.languages.registerEvaluatableExpressionProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerEvaluatableExpressionProvider")
	return createStub("unknown")
}
vscode.languages.registerInlineValuesProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerInlineValuesProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentHighlightProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDocumentHighlightProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentSymbolProvider = function (selector, provider, metaData) {
	console.log("Called stubbed function: vscode.languages.registerDocumentSymbolProvider")
	return createStub("unknown")
}
vscode.languages.registerWorkspaceSymbolProvider = function (provider) {
	console.log("Called stubbed function: vscode.languages.registerWorkspaceSymbolProvider")
	return createStub("unknown")
}
vscode.languages.registerReferenceProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerReferenceProvider")
	return createStub("unknown")
}
vscode.languages.registerRenameProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerRenameProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentSemanticTokensProvider = function (selector, provider, legend) {
	console.log("Called stubbed function: vscode.languages.registerDocumentSemanticTokensProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentRangeSemanticTokensProvider = function (selector, provider, legend) {
	console.log("Called stubbed function: vscode.languages.registerDocumentRangeSemanticTokensProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentFormattingEditProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDocumentFormattingEditProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentRangeFormattingEditProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDocumentRangeFormattingEditProvider")
	return createStub("unknown")
}
vscode.languages.registerOnTypeFormattingEditProvider = function (
	selector,
	provider,
	firstTriggerCharacter,
	moreTriggerCharacter,
) {
	console.log("Called stubbed function: vscode.languages.registerOnTypeFormattingEditProvider")
	return createStub("unknown")
}
vscode.languages.registerSignatureHelpProvider = function (selector, provider, triggerCharacters) {
	console.log("Called stubbed function: vscode.languages.registerSignatureHelpProvider")
	return createStub("unknown")
}
vscode.languages.registerSignatureHelpProvider = function (selector, provider, metadata) {
	console.log("Called stubbed function: vscode.languages.registerSignatureHelpProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentLinkProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDocumentLinkProvider")
	return createStub("unknown")
}
vscode.languages.registerColorProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerColorProvider")
	return createStub("unknown")
}
vscode.languages.registerInlayHintsProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerInlayHintsProvider")
	return createStub("unknown")
}
vscode.languages.registerFoldingRangeProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerFoldingRangeProvider")
	return createStub("unknown")
}
vscode.languages.registerSelectionRangeProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerSelectionRangeProvider")
	return createStub("unknown")
}
vscode.languages.registerCallHierarchyProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerCallHierarchyProvider")
	return createStub("unknown")
}
vscode.languages.registerTypeHierarchyProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerTypeHierarchyProvider")
	return createStub("unknown")
}
vscode.languages.registerLinkedEditingRangeProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerLinkedEditingRangeProvider")
	return createStub("unknown")
}
vscode.languages.registerDocumentDropEditProvider = function (selector, provider) {
	console.log("Called stubbed function: vscode.languages.registerDocumentDropEditProvider")
	return createStub("unknown")
}
vscode.languages.setLanguageConfiguration = function (language, configuration) {
	console.log("Called stubbed function: vscode.languages.setLanguageConfiguration")
	return createStub("unknown")
}
vscode.NotebookEditorRevealType = { Default: 0, InCenter: 0, InCenterIfOutsideViewport: 0, AtTop: 0 }
vscode.NotebookCellKind = { Markup: 0, Code: 0 }
vscode.NotebookRange = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookRange(", args, ")")
		return createStub(vscode.NotebookRange)
	}
}
vscode.NotebookCellOutputItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookCellOutputItem(", args, ")")
		return createStub(vscode.NotebookCellOutputItem)
	}
}
vscode.NotebookCellOutput = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookCellOutput(", args, ")")
		return createStub(vscode.NotebookCellOutput)
	}
}
vscode.NotebookCellData = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookCellData(", args, ")")
		return createStub(vscode.NotebookCellData)
	}
}
vscode.NotebookData = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookData(", args, ")")
		return createStub(vscode.NotebookData)
	}
}
vscode.NotebookControllerAffinity = { Default: 0, Preferred: 0 }
vscode.NotebookCellStatusBarAlignment = { Left: 0, Right: 0 }
vscode.NotebookCellStatusBarItem = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.NotebookCellStatusBarItem(", args, ")")
		return createStub(vscode.NotebookCellStatusBarItem)
	}
}
vscode.notebooks = {}
vscode.notebooks.createNotebookController = function (id, notebookType, label, handler) {
	console.log("Called stubbed function: vscode.notebooks.createNotebookController")
	return createStub("unknown")
}
vscode.notebooks.registerNotebookCellStatusBarItemProvider = function (notebookType, provider) {
	console.log("Called stubbed function: vscode.notebooks.registerNotebookCellStatusBarItemProvider")
	return createStub("unknown")
}
vscode.notebooks.createRendererMessaging = function (rendererId) {
	console.log("Called stubbed function: vscode.notebooks.createRendererMessaging")
	return createStub("unknown")
}
vscode.scm = {}
vscode.scm.inputBox = createStub("vscode.scm.inputBox")
vscode.scm.createSourceControl = function (id, label, rootUri) {
	console.log("Called stubbed function: vscode.scm.createSourceControl")
	return createStub("unknown")
}
vscode.DebugAdapterExecutable = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DebugAdapterExecutable(", args, ")")
		return createStub(vscode.DebugAdapterExecutable)
	}
}
vscode.DebugAdapterServer = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DebugAdapterServer(", args, ")")
		return createStub(vscode.DebugAdapterServer)
	}
}
vscode.DebugAdapterNamedPipeServer = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DebugAdapterNamedPipeServer(", args, ")")
		return createStub(vscode.DebugAdapterNamedPipeServer)
	}
}
vscode.DebugAdapterInlineImplementation = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.DebugAdapterInlineImplementation(", args, ")")
		return createStub(vscode.DebugAdapterInlineImplementation)
	}
}
vscode.Breakpoint = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.Breakpoint(", args, ")")
		return createStub(vscode.Breakpoint)
	}
}
vscode.SourceBreakpoint = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.SourceBreakpoint(", args, ")")
		return createStub(vscode.SourceBreakpoint)
	}
}
vscode.FunctionBreakpoint = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.FunctionBreakpoint(", args, ")")
		return createStub(vscode.FunctionBreakpoint)
	}
}
vscode.DebugConsoleMode = { Separate: 0, MergeWithParent: 0 }
vscode.DebugConfigurationProviderTriggerKind = { Initial: 0, Dynamic: 0 }
vscode.debug = {}
vscode.debug.activeDebugSession = createStub("vscode.debug.activeDebugSession")
vscode.debug.activeDebugConsole = createStub("vscode.debug.activeDebugConsole")
vscode.debug.breakpoints = createStub("vscode.debug.breakpoints")
vscode.debug.onDidChangeActiveDebugSession = createStub("vscode.debug.onDidChangeActiveDebugSession")
vscode.debug.onDidStartDebugSession = createStub("vscode.debug.onDidStartDebugSession")
vscode.debug.onDidReceiveDebugSessionCustomEvent = createStub("vscode.debug.onDidReceiveDebugSessionCustomEvent")
vscode.debug.onDidTerminateDebugSession = createStub("vscode.debug.onDidTerminateDebugSession")
vscode.debug.onDidChangeBreakpoints = createStub("vscode.debug.onDidChangeBreakpoints")
vscode.debug.registerDebugConfigurationProvider = function (debugType, provider, triggerKind) {
	console.log("Called stubbed function: vscode.debug.registerDebugConfigurationProvider")
	return createStub("unknown")
}
vscode.debug.registerDebugAdapterDescriptorFactory = function (debugType, factory) {
	console.log("Called stubbed function: vscode.debug.registerDebugAdapterDescriptorFactory")
	return createStub("unknown")
}
vscode.debug.registerDebugAdapterTrackerFactory = function (debugType, factory) {
	console.log("Called stubbed function: vscode.debug.registerDebugAdapterTrackerFactory")
	return createStub("unknown")
}
vscode.debug.startDebugging = function (folder, nameOrConfiguration, parentSessionOrOptions) {
	console.log("Called stubbed function: vscode.debug.startDebugging")
	return false
}
vscode.debug.stopDebugging = function (session) {
	console.log("Called stubbed function: vscode.debug.stopDebugging")
}
vscode.debug.addBreakpoints = function (breakpoints) {
	console.log("Called stubbed function: vscode.debug.addBreakpoints")
}
vscode.debug.removeBreakpoints = function (breakpoints) {
	console.log("Called stubbed function: vscode.debug.removeBreakpoints")
}
vscode.debug.asDebugSourceUri = function (source, session) {
	console.log("Called stubbed function: vscode.debug.asDebugSourceUri")
	return createStub("unknown")
}
vscode.extensions = {}
vscode.extensions.getExtension = function (extensionId) {
	console.log("Called stubbed function: vscode.extensions.getExtension")
	return createStub("unknown")
}
vscode.extensions.all = createStub("vscode.extensions.all")
vscode.extensions.onDidChange = createStub("vscode.extensions.onDidChange")
vscode.CommentThreadCollapsibleState = { Collapsed: 0, Expanded: 0 }
vscode.CommentMode = { Editing: 0, Preview: 0 }
vscode.CommentThreadState = { Unresolved: 0, Resolved: 0 }
vscode.comments = {}
vscode.comments.createCommentController = function (id, label) {
	console.log("Called stubbed function: vscode.comments.createCommentController")
	return createStub("unknown")
}
vscode.authentication = {}
vscode.authentication.getSession = function (providerId, scopes, options) {
	console.log("Called stubbed function: vscode.authentication.getSession")
	return Promise.resolve(null)
}
vscode.authentication.getSession = function (providerId, scopes, options) {
	console.log("Called stubbed function: vscode.authentication.getSession")
	return Promise.resolve(null)
}
vscode.authentication.getSession = function (providerId, scopes, options) {
	console.log("Called stubbed function: vscode.authentication.getSession")
	return Promise.resolve(null)
}
vscode.authentication.onDidChangeSessions = createStub("vscode.authentication.onDidChangeSessions")
vscode.authentication.registerAuthenticationProvider = function (id, label, provider, options) {
	console.log("Called stubbed function: vscode.authentication.registerAuthenticationProvider")
	return createStub("unknown")
}
vscode.l10n = {}
vscode.l10n.t = function (message, args) {
	console.log("Called stubbed function: vscode.l10n.t")
	return ""
}
vscode.l10n.t = function (message, args) {
	console.log("Called stubbed function: vscode.l10n.t")
	return ""
}
vscode.l10n.t = function (options) {
	console.log("Called stubbed function: vscode.l10n.t")
	return ""
}
vscode.l10n.bundle = createStub("vscode.l10n.bundle")
vscode.l10n.uri = createStub("vscode.l10n.uri")
vscode.tests = {}
vscode.tests.createTestController = function (id, label) {
	console.log("Called stubbed function: vscode.tests.createTestController")
	return createStub("unknown")
}
vscode.TestRunProfileKind = { Run: 0, Debug: 0, Coverage: 0 }
vscode.TestTag = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TestTag(", args, ")")
		return createStub(vscode.TestTag)
	}
}
vscode.TestRunRequest = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TestRunRequest(", args, ")")
		return createStub(vscode.TestRunRequest)
	}
}
vscode.TestMessage = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TestMessage(", args, ")")
		return createStub(vscode.TestMessage)
	}
}
vscode.TabInputText = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputText(", args, ")")
		return createStub(vscode.TabInputText)
	}
}
vscode.TabInputTextDiff = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputTextDiff(", args, ")")
		return createStub(vscode.TabInputTextDiff)
	}
}
vscode.TabInputCustom = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputCustom(", args, ")")
		return createStub(vscode.TabInputCustom)
	}
}
vscode.TabInputWebview = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputWebview(", args, ")")
		return createStub(vscode.TabInputWebview)
	}
}
vscode.TabInputNotebook = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputNotebook(", args, ")")
		return createStub(vscode.TabInputNotebook)
	}
}
vscode.TabInputNotebookDiff = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputNotebookDiff(", args, ")")
		return createStub(vscode.TabInputNotebookDiff)
	}
}
vscode.TabInputTerminal = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TabInputTerminal(", args, ")")
		return createStub(vscode.TabInputTerminal)
	}
}
vscode.TelemetryTrustedValue = class {
	constructor(...args) {
		console.log("Constructed stubbed class: new vscode.TelemetryTrustedValue(", args, ")")
		return createStub(vscode.TelemetryTrustedValue)
	}
}
module.exports = vscode
console.log("Finished loading stubs")
