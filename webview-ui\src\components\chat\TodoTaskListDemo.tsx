import React, { useState } from "react"
import TodoTaskList, { TodoTask, TaskStatus } from "./TodoTaskList"
import styled from "styled-components"

const DemoContainer = styled.div`
	padding: 20px;
	max-width: 600px;
	margin: 0 auto;
	background: var(--vscode-editor-background);
	min-height: 100vh;
`

const DemoTitle = styled.h1`
	color: var(--vscode-foreground);
	font-size: 24px;
	margin-bottom: 20px;
	text-align: center;
`

const DemoDescription = styled.p`
	color: var(--vscode-descriptionForeground);
	font-size: 14px;
	margin-bottom: 30px;
	text-align: center;
	line-height: 1.5;
`

const MockInputArea = styled.div`
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 4px;
	padding: 12px;
	margin-top: 10px;
	min-height: 100px;
	color: var(--vscode-input-foreground);
	font-family: var(--vscode-font-family);
	font-size: 14px;
	
	&::before {
		content: "这里是模拟的输入框区域...";
		color: var(--vscode-input-placeholderForeground);
		opacity: 0.7;
	}
`

// 初始演示数据
const initialTasks: TodoTask[] = [
	{
		id: "1",
		title: "设计todo组件界面",
		description: "创建可折叠的任务列表组件，支持四种状态显示",
		status: TaskStatus.COMPLETED,
		createdAt: new Date(Date.now() - 86400000), // 1天前
		updatedAt: new Date(Date.now() - 3600000)   // 1小时前
	},
	{
		id: "2", 
		title: "实现任务状态管理",
		description: "添加新建、进行中、完成、失败四种状态，支持状态切换",
		status: TaskStatus.IN_PROGRESS,
		createdAt: new Date(Date.now() - 43200000), // 12小时前
		updatedAt: new Date(Date.now() - 1800000)   // 30分钟前
	},
	{
		id: "3",
		title: "集成到输入框组件",
		description: "将todo列表固定在输入框上方，实现完整的UI集成",
		status: TaskStatus.NEW,
		createdAt: new Date(Date.now() - 21600000), // 6小时前
		updatedAt: new Date(Date.now() - 21600000)  // 6小时前
	},
	{
		id: "4",
		title: "添加任务管理功能",
		description: "支持添加、编辑、删除任务，以及状态切换功能",
		status: TaskStatus.NEW,
		createdAt: new Date(Date.now() - 10800000), // 3小时前
		updatedAt: new Date(Date.now() - 10800000)  // 3小时前
	},
	{
		id: "5",
		title: "测试错误处理",
		description: "测试组件在各种错误情况下的表现",
		status: TaskStatus.FAILED,
		createdAt: new Date(Date.now() - 7200000),  // 2小时前
		updatedAt: new Date(Date.now() - 3600000)   // 1小时前
	}
]

const TodoTaskListDemo: React.FC = () => {
	const [tasks, setTasks] = useState<TodoTask[]>(initialTasks)

	const handleTaskAdd = (newTask: Omit<TodoTask, 'id' | 'createdAt' | 'updatedAt'>) => {
		const task: TodoTask = {
			...newTask,
			id: Date.now().toString(),
			createdAt: new Date(),
			updatedAt: new Date()
		}
		setTasks(prevTasks => [...prevTasks, task])
	}

	const handleTaskUpdate = (taskId: string, updates: Partial<TodoTask>) => {
		setTasks(prevTasks => 
			prevTasks.map(task => 
				task.id === taskId 
					? { ...task, ...updates, updatedAt: new Date() }
					: task
			)
		)
	}

	const handleTaskDelete = (taskId: string) => {
		setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId))
	}

	return (
		<DemoContainer>
			<DemoTitle>Todo Task List 组件演示</DemoTitle>
			<DemoDescription>
				这是一个可折叠的待办任务列表组件，支持四种任务状态：
				<br />
				🔵 新建 (蓝色圆圈) → 🟡 进行中 (黄色同步图标) → ✅ 完成 (绿色对勾) 
				<br />
				❌ 失败 (红色错误图标)
				<br />
				<br />
				功能特性：
				<br />
				• 点击左上角箭头或右侧按钮可展开/收起列表
				<br />
				• 点击任务状态图标可切换状态 (新建→进行中→完成→新建)
				<br />
				• 鼠标悬停任务项显示删除按钮
				<br />
				• 支持添加新任务
				<br />
				• 任务计数显示 (已完成/总数)
			</DemoDescription>
			
			<TodoTaskList 
				tasks={tasks}
				onTaskAdd={handleTaskAdd}
				onTaskUpdate={handleTaskUpdate}
				onTaskDelete={handleTaskDelete}
			/>
			
			<MockInputArea />
		</DemoContainer>
	)
}

export default TodoTaskListDemo
