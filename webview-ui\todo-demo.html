<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo Task List 组件演示</title>
    <style>
        :root {
            --vscode-foreground: #cccccc;
            --vscode-background: #1e1e1e;
            --vscode-editor-background: #1e1e1e;
            --vscode-input-background: #3c3c3c;
            --vscode-input-border: #464647;
            --vscode-input-foreground: #cccccc;
            --vscode-input-placeholderForeground: #767676;
            --vscode-descriptionForeground: #9d9d9d;
            --vscode-list-hoverBackground: #2a2d2e;
            --vscode-widget-border: #464647;
            --vscode-badge-background: #4d4d4d;
            --vscode-button-foreground: #ffffff;
            --vscode-button-background: #0e639c;
            --vscode-button-hoverBackground: #1177bb;
            --vscode-charts-blue: #75beff;
            --vscode-charts-yellow: #ffcc02;
            --vscode-charts-green: #89d185;
            --vscode-errorForeground: #f85149;
            --vscode-focusBorder: #007acc;
            --vscode-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            background: var(--vscode-editor-background);
            color: var(--vscode-foreground);
            font-family: var(--vscode-font-family);
            font-size: 14px;
        }

        .demo-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-title {
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-description {
            color: var(--vscode-descriptionForeground);
            margin-bottom: 30px;
            text-align: center;
            line-height: 1.5;
        }

        .alignment-demo {
            position: relative;
            margin-bottom: 20px;
        }

        .alignment-guide {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 1px;
            background: var(--vscode-charts-red);
            opacity: 0.5;
            z-index: 10;
            pointer-events: none;
        }

        .alignment-guide.left {
            left: 15px;
        }

        .alignment-guide.right {
            right: 15px;
        }

        .alignment-toggle {
            margin-bottom: 10px;
            text-align: center;
        }

        .alignment-toggle button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 4px 8px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 11px;
        }

        .alignment-toggle button:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .todo-container {
            position: relative;
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px 2px 0 0;
            margin: 0 15px;
            margin-bottom: 0px;
            transition: all 0.2s ease-in-out;
            overflow: hidden;
        }

        .todo-container.expanded {
            border-bottom: 1px solid var(--vscode-input-border);
        }

        .todo-container.collapsed {
            border-bottom: none;
        }

        .todo-container.expanded {
            max-height: 300px;
        }

        .todo-container.collapsed {
            max-height: 40px;
        }

        .todo-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: var(--vscode-input-background);
            cursor: pointer;
            user-select: none;
        }

        .todo-header:hover {
            background: var(--vscode-list-hoverBackground);
        }

        .todo-header-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .todo-title {
            font-size: 12px;
            font-weight: 500;
        }

        .task-count {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            background: var(--vscode-badge-background);
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        .todo-list {
            padding: 8px 12px;
            max-height: 240px;
            overflow-y: auto;
        }

        .add-task-btn {
            width: 100%;
            margin-bottom: 8px;
            padding: 6px 12px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 11px;
        }

        .add-task-btn:hover {
            background: var(--vscode-button-hoverBackground);
        }

        .task-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
            border-bottom: 1px solid var(--vscode-widget-border);
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover .task-actions {
            opacity: 1;
        }

        .task-icon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            cursor: pointer;
        }

        .task-icon.new { color: var(--vscode-charts-blue); }
        .task-icon.in-progress { color: var(--vscode-charts-yellow); }
        .task-icon.completed { color: var(--vscode-charts-green); }
        .task-icon.failed { color: var(--vscode-errorForeground); }

        .task-content {
            flex: 1;
            min-width: 0;
        }

        .task-title {
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .task-description {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 2px;
        }

        .task-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }

        .task-action-btn {
            padding: 2px 4px;
            background: transparent;
            border: none;
            color: var(--vscode-foreground);
            cursor: pointer;
            border-radius: 2px;
            font-size: 10px;
        }

        .task-action-btn:hover {
            background: var(--vscode-list-hoverBackground);
        }

        .mock-input {
            background: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 0 0 2px 2px;
            border-top: none;
            margin: 0 15px;
            margin-top: 0px;
            padding: 12px;
            min-height: 100px;
            color: var(--vscode-input-placeholderForeground);
            opacity: 0.7;
            box-sizing: border-box;
        }

        .empty-state {
            text-align: center;
            padding: 20px;
            color: var(--vscode-descriptionForeground);
            font-size: 11px;
        }

        /* 图标字体 */
        .icon {
            font-family: 'codicon';
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .icon-chevron-right::before { content: '\\eab6'; }
        .icon-chevron-down::before { content: '\\eab4'; }
        .icon-chevron-up::before { content: '\\eab7'; }
        .icon-circle-outline::before { content: '\\eabc'; }
        .icon-sync::before { content: '\\ea77'; }
        .icon-check::before { content: '\\eab2'; }
        .icon-error::before { content: '\\ea87'; }
        .icon-add::before { content: '\\ea60'; }
        .icon-trash::before { content: '\\ea81'; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Todo Task List 组件演示</h1>
        <div class="demo-description">
            这是一个可折叠的待办任务列表组件，支持四种任务状态：<br>
            🔵 新建 (蓝色圆圈) → 🟡 进行中 (黄色同步图标) → ✅ 完成 (绿色对勾)<br>
            ❌ 失败 (红色错误图标)<br><br>
            功能特性：<br>
            • 点击左上角箭头或右侧按钮可展开/收起列表<br>
            • 点击任务状态图标可切换状态 (新建→进行中→完成→新建)<br>
            • 鼠标悬停任务项显示删除按钮<br>
            • 支持添加新任务<br>
            • 任务计数显示 (已完成/总数)<br>
            • <strong>边框对齐</strong>：外框与ChatTextArea的边框线完美对齐
        </div>

        <div class="alignment-toggle">
            <button onclick="toggleAlignmentGuides()">显示/隐藏边框对齐辅助线</button>
        </div>

        <div class="alignment-demo">
            <div class="alignment-guide left" id="leftGuide" style="display: none;"></div>
            <div class="alignment-guide right" id="rightGuide" style="display: none;"></div>

            <div class="todo-container collapsed" id="todoContainer">
                <div class="todo-header" onclick="toggleTodo()">
                    <div class="todo-header-left">
                        <span class="icon icon-chevron-right" id="chevronIcon"></span>
                        <span class="todo-title">待办任务</span>
                        <span class="task-count" id="taskCount">2/5</span>
                    </div>
                    <button class="task-action-btn" onclick="event.stopPropagation(); toggleTodo()">
                        <span class="icon icon-chevron-down" id="toggleIcon"></span>
                    </button>
                </div>
            
            <div class="todo-list" id="todoList">
                <button class="add-task-btn" onclick="addTask()">
                    <span class="icon icon-add"></span> 添加任务
                </button>
                
                <div class="task-item">
                    <div class="task-icon completed" onclick="toggleTaskStatus(this, 'completed')">
                        <span class="icon icon-check"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">设计todo组件界面</div>
                        <div class="task-description">创建可折叠的任务列表组件，支持四种状态显示</div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                </div>
                
                <div class="task-item">
                    <div class="task-icon in-progress" onclick="toggleTaskStatus(this, 'in-progress')">
                        <span class="icon icon-sync"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">实现任务状态管理</div>
                        <div class="task-description">添加新建、进行中、完成、失败四种状态，支持状态切换</div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                </div>
                
                <div class="task-item">
                    <div class="task-icon new" onclick="toggleTaskStatus(this, 'new')">
                        <span class="icon icon-circle-outline"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">集成到输入框组件</div>
                        <div class="task-description">将todo列表固定在输入框上方，实现完整的UI集成</div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                </div>
                
                <div class="task-item">
                    <div class="task-icon new" onclick="toggleTaskStatus(this, 'new')">
                        <span class="icon icon-circle-outline"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">添加任务管理功能</div>
                        <div class="task-description">支持添加、编辑、删除任务，以及状态切换功能</div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                </div>
                
                <div class="task-item">
                    <div class="task-icon failed" onclick="toggleTaskStatus(this, 'failed')">
                        <span class="icon icon-error"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">测试错误处理</div>
                        <div class="task-description">测试组件在各种错误情况下的表现</div>
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="mock-input">
                这里是模拟的输入框区域...
            </div>
        </div>
    </div>

    <script>
        let isExpanded = false;

        function toggleTodo() {
            const container = document.getElementById('todoContainer');
            const chevronIcon = document.getElementById('chevronIcon');
            const toggleIcon = document.getElementById('toggleIcon');

            isExpanded = !isExpanded;

            if (isExpanded) {
                container.classList.remove('collapsed');
                container.classList.add('expanded');
                chevronIcon.className = 'icon icon-chevron-down';
                toggleIcon.className = 'icon icon-chevron-up';
            } else {
                container.classList.remove('expanded');
                container.classList.add('collapsed');
                chevronIcon.className = 'icon icon-chevron-right';
                toggleIcon.className = 'icon icon-chevron-down';
            }
        }

        function toggleTaskStatus(iconElement, currentStatus) {
            const statusMap = {
                'new': { next: 'in-progress', icon: 'icon-sync', class: 'in-progress' },
                'in-progress': { next: 'completed', icon: 'icon-check', class: 'completed' },
                'completed': { next: 'new', icon: 'icon-circle-outline', class: 'new' },
                'failed': { next: 'new', icon: 'icon-circle-outline', class: 'new' }
            };
            
            const nextStatus = statusMap[currentStatus];
            if (nextStatus) {
                iconElement.className = `task-icon ${nextStatus.class}`;
                iconElement.querySelector('.icon').className = `icon ${nextStatus.icon}`;
            }
            
            updateTaskCount();
        }

        function deleteTask(buttonElement) {
            if (confirm('确定要删除这个任务吗？')) {
                const taskItem = buttonElement.closest('.task-item');
                taskItem.remove();
                updateTaskCount();
            }
        }

        function addTask() {
            const title = prompt('请输入任务标题:');
            if (title && title.trim()) {
                const description = prompt('请输入任务描述 (可选):') || '';
                
                const taskItem = document.createElement('div');
                taskItem.className = 'task-item';
                taskItem.innerHTML = `
                    <div class="task-icon new" onclick="toggleTaskStatus(this, 'new')">
                        <span class="icon icon-circle-outline"></span>
                    </div>
                    <div class="task-content">
                        <div class="task-title">${title.trim()}</div>
                        ${description.trim() ? `<div class="task-description">${description.trim()}</div>` : ''}
                    </div>
                    <div class="task-actions">
                        <button class="task-action-btn" onclick="deleteTask(this)" title="删除任务">
                            <span class="icon icon-trash"></span>
                        </button>
                    </div>
                `;
                
                document.getElementById('todoList').appendChild(taskItem);
                updateTaskCount();
            }
        }

        function updateTaskCount() {
            const taskItems = document.querySelectorAll('.task-item');
            const completedTasks = document.querySelectorAll('.task-icon.completed').length;
            const totalTasks = taskItems.length;
            
            document.getElementById('taskCount').textContent = `${completedTasks}/${totalTasks}`;
        }

        // 初始化任务计数
        updateTaskCount();

        // 对齐辅助线控制
        let guidesVisible = false;
        function toggleAlignmentGuides() {
            guidesVisible = !guidesVisible;
            const leftGuide = document.getElementById('leftGuide');
            const rightGuide = document.getElementById('rightGuide');

            if (guidesVisible) {
                leftGuide.style.display = 'block';
                rightGuide.style.display = 'block';
            } else {
                leftGuide.style.display = 'none';
                rightGuide.style.display = 'none';
            }
        }
    </script>
</body>
</html>
