import React from "react"
import QuotedMessagePreview from "@/components/chat/QuotedMessagePreview"
import ChatTextArea from "@/components/chat/ChatTextArea"
import TodoTaskList from "@/components/chat/TodoTaskList"
import { ChatState, MessageHandlers, ScrollBehavior } from "../../types/chatTypes"
import styled from "styled-components"

// Wrapper to ensure ChatTextArea connects seamlessly with TodoTaskList
const ChatTextAreaWrapper = styled.div`
	/* 确保ChatTextArea的边框层与TodoTaskList对齐，但保持完整边框 */
	& > div:first-child > div:first-child > div[style*="inset"] {
		border-radius: 0 0 2px 2px !important;
	}
`

interface InputSectionProps {
	chatState: ChatState
	messageHandlers: MessageHandlers
	scrollBehavior: ScrollBehavior
	placeholderText: string
	shouldDisableFilesAndImages: boolean
	selectFilesAndImages: () => Promise<void>
}

/**
 * Input section including quoted message preview and chat text area
 */
export const InputSection: React.FC<InputSectionProps> = ({
	chatState,
	messageHandlers,
	scrollBehavior,
	placeholderText,
	shouldDisableFilesAndImages,
	selectFilesAndImages,
}) => {
	const {
		activeQuote,
		setActiveQuote,
		isTextAreaFocused,
		inputValue,
		setInputValue,
		sendingDisabled,
		selectedImages,
		setSelectedImages,
		selectedFiles,
		setSelectedFiles,
		textAreaRef,
		handleFocusChange,
	} = chatState

	const { isAtBottom, scrollToBottomAuto } = scrollBehavior

	return (
		<>
			<TodoTaskList />

			{activeQuote && (
				<div style={{ marginBottom: "-12px", marginTop: "10px" }}>
					<QuotedMessagePreview
						text={activeQuote}
						onDismiss={() => setActiveQuote(null)}
						isFocused={isTextAreaFocused}
					/>
				</div>
			)}

			<ChatTextAreaWrapper>
				<ChatTextArea
					ref={textAreaRef}
					onFocusChange={handleFocusChange}
					activeQuote={activeQuote}
					inputValue={inputValue}
					setInputValue={setInputValue}
					sendingDisabled={sendingDisabled}
					placeholderText={placeholderText}
					selectedImages={selectedImages}
					setSelectedImages={setSelectedImages}
					setSelectedFiles={setSelectedFiles}
					selectedFiles={selectedFiles}
					onSend={() => messageHandlers.handleSendMessage(inputValue, selectedImages, selectedFiles)}
					onSelectFilesAndImages={selectFilesAndImages}
					shouldDisableFilesAndImages={shouldDisableFilesAndImages}
					onHeightChange={() => {
						if (isAtBottom) {
							scrollToBottomAuto()
						}
					}}
				/>
			</ChatTextAreaWrapper>
		</>
	)
}
