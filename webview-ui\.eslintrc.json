{
	"root": true,
	"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"],
	"parser": "@typescript-eslint/parser",
	"parserOptions": {
		"ecmaVersion": 2020,
		"sourceType": "module"
	},
	"plugins": ["@typescript-eslint", "react-hooks", "react-refresh", "eslint-rules"],
	"env": {
		"browser": true,
		"es2020": true
	},
	"rules": {
		"react-hooks/rules-of-hooks": "error",
		// "react-refresh/only-export-components": [
		//     "warn",
		//     {
		//         "allowConstantExport": true
		//     }
		// ],
		"@typescript-eslint/no-unused-vars": "off",
		"@typescript-eslint/no-explicit-any": "off",
		"@typescript-eslint/no-empty-object-type": "off",
		"no-case-declarations": "off",
		"react-hooks/exhaustive-deps": "off",
		"prefer-const": "off",
		"no-extra-semi": "off",
		"eslint-rules/no-protobuf-object-literals": "error",
		"eslint-rules/no-grpc-client-object-literals": "error",
		"eslint-rules/no-direct-vscode-api": "warn",
		"no-restricted-syntax": [
			"error",
			{
				"selector": "VariableDeclarator[id.type=\"ObjectPattern\"][init.object.name=\"process\"][init.property.name=\"env\"]",
				"message": "Use process.env.VARIABLE_NAME directly instead of destructuring"
			}
		]
	},
	"ignorePatterns": ["build"]
}
