# TodoTaskList 组件

一个可折叠的待办任务列表组件，设计用于在cline的输入框上方显示。

## 功能特性

### 🎯 核心功能
- **可折叠界面**: 点击左上角箭头或右侧按钮展开/收起列表
- **任务状态管理**: 支持四种任务状态，点击图标可切换状态
- **任务操作**: 添加、删除任务，鼠标悬停显示操作按钮
- **任务计数**: 实时显示已完成/总任务数

### 📊 任务状态
1. **🔵 新建 (NEW)** - 蓝色圆圈图标 (`codicon-circle-outline`)
2. **🟡 进行中 (IN_PROGRESS)** - 黄色同步图标 (`codicon-sync`)  
3. **✅ 完成 (COMPLETED)** - 绿色对勾图标 (`codicon-check`)
4. **❌ 失败 (FAILED)** - 红色错误图标 (`codicon-error`)

### 🔄 状态切换逻辑
- 新建 → 进行中 → 完成 → 新建 (循环)
- 失败 → 新建

## 使用方法

### 基本用法

```tsx
import TodoTaskList, { TodoTask, TaskStatus } from '@/components/chat/TodoTaskList'

// 基本使用 (只读模式)
<TodoTaskList tasks={tasks} />

// 完整功能模式
<TodoTaskList 
  tasks={tasks}
  onTaskAdd={handleTaskAdd}
  onTaskUpdate={handleTaskUpdate}
  onTaskDelete={handleTaskDelete}
/>
```

### 数据结构

```tsx
interface TodoTask {
  id: string
  title: string
  description?: string
  status: TaskStatus
  createdAt: Date
  updatedAt: Date
}

enum TaskStatus {
  NEW = "new",
  IN_PROGRESS = "in_progress", 
  COMPLETED = "completed",
  FAILED = "failed"
}
```

### 事件处理

```tsx
const [tasks, setTasks] = useState<TodoTask[]>([])

// 添加任务
const handleTaskAdd = (newTask: Omit<TodoTask, 'id' | 'createdAt' | 'updatedAt'>) => {
  const task: TodoTask = {
    ...newTask,
    id: Date.now().toString(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
  setTasks(prev => [...prev, task])
}

// 更新任务
const handleTaskUpdate = (taskId: string, updates: Partial<TodoTask>) => {
  setTasks(prev => 
    prev.map(task => 
      task.id === taskId 
        ? { ...task, ...updates, updatedAt: new Date() }
        : task
    )
  )
}

// 删除任务
const handleTaskDelete = (taskId: string) => {
  setTasks(prev => prev.filter(task => task.id !== taskId))
}
```

## 组件属性

| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `tasks` | `TodoTask[]` | 否 | `mockTasks` | 任务列表数据 |
| `onTaskAdd` | `(task: Omit<TodoTask, 'id' \| 'createdAt' \| 'updatedAt'>) => void` | 否 | - | 添加任务回调 |
| `onTaskUpdate` | `(taskId: string, updates: Partial<TodoTask>) => void` | 否 | - | 更新任务回调 |
| `onTaskDelete` | `(taskId: string) => void` | 否 | - | 删除任务回调 |

## 样式定制

组件使用VSCode主题变量，自动适配当前主题：

```css
/* 主要颜色变量 */
--vscode-input-background
--vscode-input-border
--vscode-foreground
--vscode-descriptionForeground
--vscode-charts-blue (新建状态)
--vscode-charts-yellow (进行中状态)
--vscode-charts-green (完成状态)
--vscode-errorForeground (失败状态)
```

## 集成示例

在InputSection中集成：

```tsx
// InputSection.tsx
import TodoTaskList from "@/components/chat/TodoTaskList"

export const InputSection: React.FC<InputSectionProps> = ({...props}) => {
  return (
    <>
      <TodoTaskList />
      {/* 其他组件 */}
    </>
  )
}
```

## 演示

查看 `todo-demo.html` 文件获取完整的交互演示。

## 技术实现

- **框架**: React + TypeScript
- **样式**: Styled Components
- **图标**: VSCode Codicons
- **主题**: VSCode CSS变量
- **动画**: CSS Transitions

## 注意事项

1. 组件依赖VSCode Webview环境和相关CSS变量
2. 图标使用VSCode Codicons字体
3. 任务ID应保证唯一性
4. 删除操作会弹出确认对话框
5. 添加任务使用原生prompt对话框（可根据需要替换为自定义组件）

## 未来改进

- [ ] 支持任务拖拽排序
- [ ] 支持任务分类/标签
- [ ] 支持任务优先级
- [ ] 支持任务截止日期
- [ ] 支持任务搜索/过滤
- [ ] 支持批量操作
- [ ] 支持任务导入/导出
- [ ] 支持键盘快捷键
- [ ] 支持自定义任务模板
