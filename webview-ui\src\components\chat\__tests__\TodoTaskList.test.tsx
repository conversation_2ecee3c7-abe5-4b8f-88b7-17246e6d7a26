import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import TodoTaskList, { TodoTask, TaskStatus } from '../TodoTaskList'

// Mock styled-components
jest.mock('styled-components', () => ({
  __esModule: true,
  default: (component: any) => component,
}))

// Mock VSCode components
jest.mock('@vscode/webview-ui-toolkit/react', () => ({
  VSCodeButton: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}))

const mockTasks: TodoTask[] = [
  {
    id: '1',
    title: '测试任务1',
    description: '这是一个测试任务',
    status: TaskStatus.NEW,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    title: '测试任务2',
    status: TaskStatus.COMPLETED,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

describe('TodoTaskList', () => {
  it('应该渲染任务列表', () => {
    render(<TodoTaskList tasks={mockTasks} />)
    
    expect(screen.getByText('待办任务')).toBeInTheDocument()
    expect(screen.getByText('1/2')).toBeInTheDocument() // 1个完成，总共2个
  })

  it('应该显示正确的任务计数', () => {
    render(<TodoTaskList tasks={mockTasks} />)
    
    // 1个完成任务，总共2个任务
    expect(screen.getByText('1/2')).toBeInTheDocument()
  })

  it('应该在展开时显示任务列表', () => {
    render(<TodoTaskList tasks={mockTasks} />)
    
    // 初始状态是收起的，任务不可见
    expect(screen.queryByText('测试任务1')).not.toBeInTheDocument()
    
    // 点击展开
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    // 现在任务应该可见
    expect(screen.getByText('测试任务1')).toBeInTheDocument()
    expect(screen.getByText('测试任务2')).toBeInTheDocument()
  })

  it('应该调用onTaskAdd回调', () => {
    const mockOnTaskAdd = jest.fn()
    
    // Mock window.prompt
    const originalPrompt = window.prompt
    window.prompt = jest.fn()
      .mockReturnValueOnce('新任务标题')
      .mockReturnValueOnce('新任务描述')
    
    render(
      <TodoTaskList 
        tasks={mockTasks} 
        onTaskAdd={mockOnTaskAdd}
      />
    )
    
    // 展开列表
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    // 点击添加任务按钮
    const addButton = screen.getByText('添加任务')
    fireEvent.click(addButton)
    
    expect(mockOnTaskAdd).toHaveBeenCalledWith({
      title: '新任务标题',
      description: '新任务描述',
      status: TaskStatus.NEW,
    })
    
    // 恢复原始prompt
    window.prompt = originalPrompt
  })

  it('应该调用onTaskUpdate回调', () => {
    const mockOnTaskUpdate = jest.fn()
    
    render(
      <TodoTaskList 
        tasks={mockTasks} 
        onTaskUpdate={mockOnTaskUpdate}
      />
    )
    
    // 展开列表
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    // 找到第一个任务的状态图标并点击
    const taskIcons = screen.getAllByRole('button')
    const statusIcon = taskIcons.find(button => 
      button.getAttribute('style')?.includes('cursor: pointer')
    )
    
    if (statusIcon) {
      fireEvent.click(statusIcon)
      
      expect(mockOnTaskUpdate).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          status: TaskStatus.IN_PROGRESS,
          updatedAt: expect.any(Date),
        })
      )
    }
  })

  it('应该调用onTaskDelete回调', () => {
    const mockOnTaskDelete = jest.fn()
    
    // Mock window.confirm
    const originalConfirm = window.confirm
    window.confirm = jest.fn().mockReturnValue(true)
    
    render(
      <TodoTaskList 
        tasks={mockTasks} 
        onTaskDelete={mockOnTaskDelete}
      />
    )
    
    // 展开列表
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    // 找到删除按钮（通过title属性）
    const deleteButton = screen.getAllByTitle('删除任务')[0]
    fireEvent.click(deleteButton)
    
    expect(mockOnTaskDelete).toHaveBeenCalledWith('1')
    
    // 恢复原始confirm
    window.confirm = originalConfirm
  })

  it('应该显示空状态', () => {
    render(<TodoTaskList tasks={[]} />)
    
    // 展开列表
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    expect(screen.getByText('暂无任务')).toBeInTheDocument()
  })

  it('应该正确显示任务状态图标', () => {
    const tasksWithAllStatuses: TodoTask[] = [
      {
        id: '1',
        title: '新建任务',
        status: TaskStatus.NEW,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        title: '进行中任务',
        status: TaskStatus.IN_PROGRESS,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '3',
        title: '完成任务',
        status: TaskStatus.COMPLETED,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '4',
        title: '失败任务',
        status: TaskStatus.FAILED,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]
    
    render(<TodoTaskList tasks={tasksWithAllStatuses} />)
    
    // 展开列表
    const header = screen.getByText('待办任务').closest('div')
    fireEvent.click(header!)
    
    // 验证所有任务都显示
    expect(screen.getByText('新建任务')).toBeInTheDocument()
    expect(screen.getByText('进行中任务')).toBeInTheDocument()
    expect(screen.getByText('完成任务')).toBeInTheDocument()
    expect(screen.getByText('失败任务')).toBeInTheDocument()
    
    // 验证任务计数 (1个完成，总共4个)
    expect(screen.getByText('1/4')).toBeInTheDocument()
  })
})
